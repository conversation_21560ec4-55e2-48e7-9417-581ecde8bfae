<?php
session_start();

// Check if user is logged in
if (!isset($_SESSION["loggedin"]) || $_SESSION["loggedin"] !== true) {
    header("location: ../index.php");
    exit;
}

require_once 'config.php';
require_once 'language.php';

// Get user information
$user_id = $_SESSION["id"];
$sql = "SELECT * FROM users WHERE id = ?";
$stmt = execute_query($link, $sql, "i", [$user_id]);

if (!$stmt) {
    die("Error retrieving user information");
}

$result = mysqli_stmt_get_result($stmt);
$user = mysqli_fetch_assoc($result);
mysqli_stmt_close($stmt);

if (!$user) {
    die("User not found");
}

// Handle form submission
if ($_SERVER["REQUEST_METHOD"] == "POST" && isset($_POST['update_nickname'])) {
    $new_nickname = trim($_POST['nickname']);
    
    if (empty($new_nickname)) {
        $error_message = "Nickname cannot be empty.";
    } else {
        $update_sql = "UPDATE users SET nickname = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?";
        $update_stmt = execute_query($link, $update_sql, "si", [$new_nickname, $user_id]);
        
        if ($update_stmt) {
            mysqli_stmt_close($update_stmt);
            $success_message = "Nickname updated successfully!";
            $user['nickname'] = $new_nickname; // Update local data
        } else {
            $error_message = "Error updating nickname. Please try again.";
        }
    }
}

close_db_connection($link);
?>

<!DOCTYPE html>
<html lang="<?= $lang ?>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Account Settings - KelvinKMS.com</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            background-color: #a48f19; 
            color: white; 
            margin: 0; 
            padding: 20px; 
        }
        .container { 
            max-width: 800px; 
            margin: auto; 
            background-color: #2b9869; 
            padding: 30px; 
            border-radius: 14px; 
            box-shadow: 0 5px 15px rgba(0,0,0,0.5); 
        }
        h1 { 
            color: #00ffff; 
            text-align: center; 
            margin-bottom: 30px;
        }
        .back-btn {
            position: fixed;
            top: 10px;
            right: 10px;
            padding: 5px 10px;
            background: #0087ff;
            color: white;
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 10px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
        }
        .back-btn:hover {
            background: #00e5ffff;
        }
        .profile-section {
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
        }
        .profile-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        }
        .profile-row:last-child {
            border-bottom: none;
        }
        .profile-label {
            font-weight: bold;
            color: #00ffff;
            min-width: 150px;
        }
        .profile-value {
            flex: 1;
            text-align: right;
        }
        .editable-section {
            background: rgba(255, 255, 255, 0.15);
            padding: 20px;
            border-radius: 10px;
            margin-top: 20px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        .form-group label {
            display: block;
            margin-bottom: 5px;
            color: #00ffff;
            font-weight: bold;
        }
        .form-group input {
            width: 100%;
            padding: 10px;
            border: 2px solid #00ffff;
            border-radius: 5px;
            background: rgba(255, 255, 255, 0.9);
            color: #000;
            font-size: 16px;
        }
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin-right: 10px;
        }
        .btn-primary {
            background: #007bff;
            color: white;
        }
        .btn-primary:hover {
            background: #0056b3;
        }
        .alert {
            padding: 15px;
            margin-bottom: 20px;
            border-radius: 5px;
        }
        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .readonly-note {
            font-size: 14px;
            color: #ffc107;
            font-style: italic;
            margin-top: 10px;
        }
    </style>
</head>
<body>
    <a href="member.php" class="back-btn">← Back to Member Area</a>
    
    <div class="container">
        <h1>⚙️ Account Settings</h1>
        
        <?php if (isset($success_message)): ?>
            <div class="alert alert-success"><?= htmlspecialchars($success_message) ?></div>
        <?php endif; ?>
        
        <?php if (isset($error_message)): ?>
            <div class="alert alert-error"><?= htmlspecialchars($error_message) ?></div>
        <?php endif; ?>
        
        <div class="profile-section">
            <h2>📋 Profile Information</h2>
            
            <div class="profile-row">
                <div class="profile-label">Username:</div>
                <div class="profile-value"><?= htmlspecialchars($user['username']) ?></div>
            </div>
            
            <div class="profile-row">
                <div class="profile-label">First Name:</div>
                <div class="profile-value"><?= htmlspecialchars($user['first_name']) ?></div>
            </div>
            
            <div class="profile-row">
                <div class="profile-label">Last Name:</div>
                <div class="profile-value"><?= htmlspecialchars($user['last_name']) ?></div>
            </div>
            
            <div class="profile-row">
                <div class="profile-label">Email:</div>
                <div class="profile-value"><?= htmlspecialchars($user['email']) ?></div>
            </div>
            
            <div class="profile-row">
                <div class="profile-label">Phone Number:</div>
                <div class="profile-value"><?= htmlspecialchars($user['phone_number']) ?></div>
            </div>
            
            <div class="profile-row">
                <div class="profile-label">Gender:</div>
                <div class="profile-value"><?= ucfirst(htmlspecialchars($user['gender'])) ?></div>
            </div>
            
            <div class="profile-row">
                <div class="profile-label">Birthday:</div>
                <div class="profile-value"><?= htmlspecialchars($user['birthday']) ?></div>
            </div>
            
            <div class="profile-row">
                <div class="profile-label">Language:</div>
                <div class="profile-value"><?= htmlspecialchars($user['language']) ?></div>
            </div>
            
            <div class="profile-row">
                <div class="profile-label">Member Since:</div>
                <div class="profile-value"><?= date('F j, Y', strtotime($user['created_at'])) ?></div>
            </div>
            
            <div class="profile-row">
                <div class="profile-label">Last Login:</div>
                <div class="profile-value">
                    <?= $user['last_login'] ? date('F j, Y g:i A', strtotime($user['last_login'])) : 'Never' ?>
                </div>
            </div>
        </div>
        
        <div class="editable-section">
            <h2>✏️ Editable Information</h2>
            
            <form method="POST" action="">
                <div class="form-group">
                    <label for="nickname">Nickname:</label>
                    <input type="text" id="nickname" name="nickname" value="<?= htmlspecialchars($user['nickname']) ?>" required maxlength="50">
                </div>
                
                <button type="submit" name="update_nickname" class="btn btn-primary">Update Nickname</button>
            </form>
            
            <div class="readonly-note">
                ℹ️ Other profile information can only be modified by an administrator for security reasons.
            </div>
        </div>
    </div>
</body>
</html>
