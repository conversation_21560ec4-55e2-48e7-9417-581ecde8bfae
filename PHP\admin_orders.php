<?php
session_start();

// Admin Check at the very top
if (!isset($_SESSION["loggedin"]) || $_SESSION["loggedin"] !== true || !isset($_SESSION["is_admin"]) || $_SESSION["is_admin"] !== true) {
    header("location: ../index.php");
    exit;
}

require_once 'config.php';
require_once 'language.php';
?>
<!DOCTYPE html>
<html lang="<?= $lang ?>">
<head>
    <title><?= t('admin_pc_orders_title') ?></title>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="../CSS/style.css">
    <style>
        body { font-family: Arial, sans-serif; background-color: #a48f19; margin: 0; padding: 20px; color: white; }
        .container { max-width: 1400px; margin: auto; background: rgb(5 195 182); padding: 30px; box-shadow: 0 2px 8px rgb(0 0 0); border-radius: 20px; }
        h1 { text-align: center; color: #ffffff; font-size: 32px; }
        table { width: 100%; border-collapse: collapse; margin-top: 20px; background: rgba(255, 255, 255, 0.1); border-radius: 12px; overflow: hidden; box-shadow: 0 2px 8px rgb(0 0 0); }
        th, td { border: 1px solid rgba(255, 255, 255, 0.2); padding: 12px; text-align: left; vertical-align: top; }
        th { background-color: rgba(0, 0, 0, 0.3); color: #ffffff; font-weight: bold; }
        tr:nth-child(even) { background-color: rgba(255, 255, 255, 0.1); }
        tr:hover { background-color: rgba(255, 255, 255, 0.2); }
        .order-details { max-height: 100px; overflow-y: auto; display: block; white-space: pre-wrap; }
        input[type="number"], input[type="date"], select {
            width: 95%;
            padding: 4px;
            margin-top: 5px;
            border-radius: 10px;
            border: 2px solid rgb(253, 202, 0);
            background: rgb(255 194 0);
            color: #ffffff;
            box-shadow: 0 2px 8px rgb(0 0 0);
        }
        input[type="number"]:focus, input[type="date"]:focus, select:focus {
            outline: none;
            border-color: rgba(255, 213, 0, 0.8);
            background: rgba(255, 196, 0, 0.95);
        }
        button {
            padding: 5px 5px;
            background-color: rgb(253, 202, 0);
            color: #ffffff;
            border: 2px solid rgb(253, 202, 0);
            border-radius: 10px;
            cursor: pointer;
            font-weight: bold;
            box-shadow: 0 2px 8px rgb(0 0 0);
            transition: all 0.3s ease;
        }
        button:hover {
            background-color: rgba(255, 196, 0, 0.95);
            border-color: rgba(255, 213, 0, 0.8);
        }
        .back-link {
            position: fixed;
            top: 10px;
            right: 10px;
            background-color: #00bcaa;
            color: white;
            border: 2px solid rgba(255, 255, 255, 0.3);
            padding: 5px 10px;
            border-radius: 10px;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
            z-index: 1000;
            box-shadow: 0 2px 8px rgb(0 0 0);
            transition: all 0.3s ease;
            text-decoration: none;
        }
        .back-link:hover {
            background-color: #ffbf00ff;
            border-color: rgba(255, 255, 255, 0.3);
        }
    </style>
</head>
<body>
    <div class="container">
        <a href="admin.php" class="back-link">&larr; <?= t('back_to_admin_panel') ?></a>
        <h1><?= t('admin_pc_orders_header') ?></h1>
        <div id="ordersTableContainer">
            <table>
                <thead>
                    <tr>
                        <th><?= t('order_id') ?></th>
                        <th><?= t('customer') ?></th>
                        <th><?= t('order_details') ?></th>
                        <th><?= t('status') ?></th>
                        <th><?= t('final_price') ?></th>
                        <th><?= t('eta') ?></th>
                        <th><?= t('order_date') ?></th>
                        <th><?= t('action') ?></th>
                    </tr>
                </thead>
                <tbody id="ordersTbody">
                    <!-- Orders will be loaded here by JavaScript -->
                </tbody>
            </table>
        </div>
    </div>

    <script>
    document.addEventListener('DOMContentLoaded', function() {
        const ordersTbody = document.getElementById('ordersTbody');

        function loadOrders() {
            fetch('admin_get_orders.php')
                .then(response => response.json())
                .then(data => {
                    console.log('Data received:', data);
                    if (data && data.success) {
                        renderOrders(data.orders);
                    } else {
                        ordersTbody.innerHTML = `<tr><td colspan="8">Error: ${data.message || 'Could not load orders.'}</td></tr>`;
                    }
                })
                .catch(error => {
                    console.error('Error fetching orders:', error);
                    ordersTbody.innerHTML = `<tr><td colspan="8">An error occurred while fetching orders.</td></tr>`;
                });
        }

        function renderOrders(orders) {
            ordersTbody.innerHTML = '';
            if (orders.length === 0) {
                ordersTbody.innerHTML = `<tr><td colspan="8">No orders found.</td></tr>`;
                return;
            }

            orders.forEach(order => {
                try {
                    const tr = document.createElement('tr');
                    tr.dataset.orderId = order.id;

                    const statusOptions = ['Quote Requested', 'Quote Provided', 'Order Pending', 'Payment Received', 'Order In Progress', 'Order Shipped', 'Order Delivered', 'Order Cancelled']
                        .map(s => `<option value="${s}" ${s === order.status ? 'selected' : ''}>${s}</option>`).join('');

                    // Handle potential invalid date
                    const createdAt = order.created_at ? new Date(order.created_at).toLocaleString() : 'Invalid Date';

                    tr.innerHTML = `
                        <td>${order.id}</td>
                        <td>${order.nickname} (${order.username})</td>
                        <td><div class="order-details">${escapeHTML(order.order_details)}</div></td>
                        <td><select class="status-select">${statusOptions}</select></td>
                        <td><input type="number" class="price-input" value="${order.final_price || ''}" step="0.01" placeholder="Set Price"></td>
                        <td><input type="date" class="eta-input" value="${order.estimated_completion_date || ''}"></td>
                        <td>${createdAt}</td>
                        <td><button class="update-btn">Update</button></td>
                    `;
                    ordersTbody.appendChild(tr);
                } catch (e) {
                    console.error('Failed to render order row:', order, e);
                    // Optionally, render an error row
                    const tr = document.createElement('tr');
                    tr.innerHTML = `<td colspan="8">Error rendering order #${order.id}. Please check console.</td>`;
                    ordersTbody.appendChild(tr);
                }
            });
        }
        
        function escapeHTML(str) {
            if (str === null || str === undefined) return '';
            return str.toString().replace(/[&<>"']/g, function(match) {
                return {
                    '&': '&amp;',
                    '<': '&lt;',
                    '>': '&gt;',
                    '"': '&quot;',
                    "'": '&#39;'
                }[match];
            });
        }


        ordersTbody.addEventListener('click', function(e) {
            if (e.target.classList.contains('update-btn')) {
                const tr = e.target.closest('tr');
                const orderId = tr.dataset.orderId;
                const status = tr.querySelector('.status-select').value;
                const price = tr.querySelector('.price-input').value;
                const eta = tr.querySelector('.eta-input').value;
                
                updateOrder(orderId, status, price, eta, e.target);
            }
        });

        function updateOrder(orderId, status, price, eta, button) {
            button.disabled = true;
            button.textContent = 'Updating...';

            const formData = new FormData();
            formData.append('order_id', orderId);
            formData.append('status', status);
            formData.append('final_price', price);
            formData.append('estimated_completion_date', eta);

            fetch('admin_update_order.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('Order updated successfully!');
                } else {
                    alert('Error updating order: ' + (data.message || 'Unknown error'));
                }
            })
            .catch(error => {
                console.error('Update Error:', error);
                alert('An network error occurred.');
            })
            .finally(() => {
                button.disabled = false;
                button.textContent = 'Update';
            });
        }

        loadOrders();
    });
    </script>
</body>
</html>