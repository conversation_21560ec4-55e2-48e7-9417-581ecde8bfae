<?php
// Admin PC Components Management Page
session_start();
require_once 'config.php';
require_once 'language.php';

// Check if user is admin
if (!isset($_SESSION['loggedin']) || $_SESSION['loggedin'] !== true || !isset($_SESSION['is_admin']) || $_SESSION['is_admin'] !== true) {
    header('Location: ../index.php');
    exit;
}
?>
<!DOCTYPE html>
<html lang="<?= $lang ?>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin - PC Management - KelvinKMS.com</title>
    <link rel="stylesheet" href="../CSS/custom-modal.css" />
    <style>
        body { 
            font-family: Arial, sans-serif; 
            background-color: #1a1a1a; 
            color: white; 
            margin: 0; 
            padding: 20px; 
        }
        .container { 
            max-width: 1400px; 
            margin: auto; 
            background-color: #2b2b2b; 
            padding: 30px; 
            border-radius: 14px; 
            box-shadow: 0 5px 15px rgba(0,0,0,0.5); 
        }
        h1, h2, h3 { color: #00ffff; }
        .nav-tabs {
            display: flex;
            margin-bottom: 30px;
            border-bottom: 2px solid #444;
        }
        .nav-tab {
            padding: 15px 25px;
            background: #333;
            color: white;
            border: none;
            cursor: pointer;
            margin-right: 5px;
            border-radius: 5px 5px 0 0;
        }
        .nav-tab.active {
            background: #00ffff;
            color: #000;
        }
        .tab-content {
            display: none;
        }
        .tab-content.active {
            display: block;
        }
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-weight: bold;
            margin: 5px;
        }
        .btn-primary { background: #007bff; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-warning { background: #ffc107; color: black; }
        .btn-danger { background: #dc3545; color: white; }
        .btn-secondary { background: #6c757d; color: white; }
        .table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
            background: #333;
        }
        .table th, .table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #555;
        }
        .table th {
            background: #444;
            color: #00ffff;
        }
        .table tr:hover {
            background: #3a3a3a;
        }
        .form-group {
            margin-bottom: 20px;
        }
        .form-group label {
            display: block;
            margin-bottom: 5px;
            color: #00ffff;
        }
        .form-group input, .form-group select, .form-group textarea {
            width: 100%;
            padding: 10px;
            border: 1px solid #555;
            border-radius: 5px;
            background: #444;
            color: white;
        }
        .form-row {
            display: flex;
            gap: 20px;
        }
        .form-row .form-group {
            flex: 1;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .stat-card {
            background: rgba(0,255,255,0.1);
            padding: 20px;
            border-radius: 10px;
            border: 2px solid #00ffff;
            text-align: center;
        }
        .stat-value {
            font-size: 36px;
            font-weight: bold;
            color: #00ffff;
        }
        .stat-label {
            font-size: 14px;
            color: #ccc;
            margin-top: 5px;
        }
        .search-bar {
            margin-bottom: 20px;
        }
        .search-bar input {
            width: 300px;
            padding: 10px;
            border: 1px solid #555;
            border-radius: 5px;
            background: #444;
            color: white;
        }
        .component-card {
            background: #333;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            border-left: 4px solid #00ffff;
        }
        .component-specs {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
            margin: 10px 0;
        }
        .spec-item {
            background: rgba(0,0,0,0.3);
            padding: 8px;
            border-radius: 5px;
            font-size: 12px;
        }
        .prebuilt-card {
            background: #333;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            border-left: 4px solid #ffd700;
        }
        .price-display {
            font-size: 24px;
            font-weight: bold;
            color: #00ff00;
        }
        .discount-badge {
            background: #ff6b6b;
            color: white;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 12px;
            margin-left: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 30px;">
            <h1>PC Components Management</h1>
            <div>
                <button class="btn btn-secondary" onclick="window.location.href='admin_panel.php'">Back to Admin Panel</button>
                <button class="btn btn-danger" onclick="window.location.href='logout.php'">Logout</button>
            </div>
        </div>

        <!-- Statistics -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-value" id="total-components">0</div>
                <div class="stat-label">Total Components</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="total-prebuilt">0</div>
                <div class="stat-label">Pre-built Configs</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="total-orders">0</div>
                <div class="stat-label">PC Orders</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="pending-orders">0</div>
                <div class="stat-label">Pending Orders</div>
            </div>
        </div>

        <!-- Navigation Tabs -->
        <div class="nav-tabs">
            <button class="nav-tab active" onclick="switchTab('components')">Components</button>
            <button class="nav-tab" onclick="switchTab('prebuilt')">Pre-built Configs</button>
            <button class="nav-tab" onclick="switchTab('orders')">PC Orders</button>
            <button class="nav-tab" onclick="switchTab('categories')">Categories</button>
        </div>

        <!-- Components Tab -->
        <div id="components-tab" class="tab-content active">
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                <h2>PC Components</h2>
                <button class="btn btn-success" onclick="openAddComponentModal()">Add Component</button>
            </div>
            
            <div class="search-bar">
                <input type="text" id="component-search" placeholder="Search components..." onkeyup="searchComponents()">
                <select id="category-filter" onchange="filterComponents()" style="margin-left: 10px; padding: 10px; background: #444; color: white; border: 1px solid #555;">
                    <option value="">All Categories</option>
                </select>
            </div>
            
            <div id="components-list">
                <div style="text-align: center; padding: 40px; color: #ccc;">
                    Loading components...
                </div>
            </div>
        </div>

        <!-- Pre-built Configs Tab -->
        <div id="prebuilt-tab" class="tab-content">
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                <h2>Pre-built Configurations</h2>
                <button class="btn btn-success" onclick="openAddPrebuiltModal()">Add Pre-built Config</button>
            </div>
            
            <div class="search-bar">
                <input type="text" id="prebuilt-search" placeholder="Search configurations..." onkeyup="searchPrebuilt()">
                <select id="tier-filter" onchange="filterPrebuilt()" style="margin-left: 10px; padding: 10px; background: #444; color: white; border: 1px solid #555;">
                    <option value="">All Tiers</option>
                    <option value="entry">Entry</option>
                    <option value="mid">Mid-Range</option>
                    <option value="high">High-End</option>
                    <option value="extreme">Extreme</option>
                </select>
            </div>
            
            <div id="prebuilt-list">
                <div style="text-align: center; padding: 40px; color: #ccc;">
                    Loading pre-built configurations...
                </div>
            </div>
        </div>

        <!-- PC Orders Tab -->
        <div id="orders-tab" class="tab-content">
            <h2>PC Orders Management</h2>
            
            <div class="search-bar">
                <input type="text" id="orders-search" placeholder="Search orders..." onkeyup="searchOrders()">
                <select id="status-filter" onchange="filterOrders()" style="margin-left: 10px; padding: 10px; background: #444; color: white; border: 1px solid #555;">
                    <option value="">All Status</option>
                    <option value="pending">Pending</option>
                    <option value="quoted">Quoted</option>
                    <option value="confirmed">Confirmed</option>
                    <option value="processing">Processing</option>
                    <option value="completed">Completed</option>
                    <option value="cancelled">Cancelled</option>
                </select>
            </div>
            
            <div id="orders-list">
                <div style="text-align: center; padding: 40px; color: #ccc;">
                    Loading PC orders...
                </div>
            </div>
        </div>

        <!-- Categories Tab -->
        <div id="categories-tab" class="tab-content">
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                <h2>Component Categories</h2>
                <button class="btn btn-success" onclick="openAddCategoryModal()">Add Category</button>
            </div>
            
            <div id="categories-list">
                <div style="text-align: center; padding: 40px; color: #ccc;">
                    Loading categories...
                </div>
            </div>
        </div>
    </div>

    <script>
        // Global variables
        let components = [];
        let prebuiltConfigs = [];
        let pcOrders = [];
        let categories = [];
        let currentTab = 'components';

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            loadStatistics();
            loadCategories();
            loadComponents();
            loadPrebuiltConfigs();
            loadPCOrders();
        });

        // Tab switching
        function switchTab(tabName) {
            // Hide all tabs
            document.querySelectorAll('.tab-content').forEach(tab => {
                tab.classList.remove('active');
            });
            document.querySelectorAll('.nav-tab').forEach(tab => {
                tab.classList.remove('active');
            });

            // Show selected tab
            document.getElementById(tabName + '-tab').classList.add('active');
            event.target.classList.add('active');
            currentTab = tabName;
        }

        // Load statistics
        function loadStatistics() {
            // This would typically make API calls to get real statistics
            // For now, we'll update them as we load data
        }

        // Load categories
        function loadCategories() {
            fetch('pc_components_api.php?action=get_categories', {
                credentials: 'same-origin'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    categories = data.categories;
                    populateCategoryFilter();
                    displayCategories();
                }
            })
            .catch(error => console.error('Error loading categories:', error));
        }

        function populateCategoryFilter() {
            const filter = document.getElementById('category-filter');
            filter.innerHTML = '<option value="">All Categories</option>';
            categories.forEach(category => {
                filter.innerHTML += `<option value="${category.id}">${category.category_name}</option>`;
            });
        }

        function displayCategories() {
            const container = document.getElementById('categories-list');
            if (categories.length === 0) {
                container.innerHTML = '<div style="text-align: center; padding: 40px; color: #ccc;">No categories found.</div>';
                return;
            }

            container.innerHTML = categories.map(category => `
                <div class="component-card">
                    <div style="display: flex; justify-content: space-between; align-items: center;">
                        <div>
                            <h3 style="margin: 0; color: #00ffff;">${category.category_name}</h3>
                            <p style="margin: 5px 0; color: #ccc;">${category.description || 'No description'}</p>
                            <div style="font-size: 12px; color: #888;">
                                Sort Order: ${category.sort_order} | 
                                Status: ${category.is_active ? '<span style="color: #00ff00;">Active</span>' : '<span style="color: #ff6b6b;">Inactive</span>'}
                            </div>
                        </div>
                        <div>
                            <button class="btn btn-warning" onclick="editCategory(${category.id})">Edit</button>
                            <button class="btn btn-danger" onclick="deleteCategory(${category.id})">Delete</button>
                        </div>
                    </div>
                </div>
            `).join('');
        }

        // Load components
        function loadComponents() {
            fetch('pc_components_api.php?action=get_components&active_only=0', {
                credentials: 'same-origin'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    components = data.components;
                    displayComponents();
                    updateStatistics();
                }
            })
            .catch(error => console.error('Error loading components:', error));
        }

        function displayComponents() {
            const container = document.getElementById('components-list');
            if (components.length === 0) {
                container.innerHTML = '<div style="text-align: center; padding: 40px; color: #ccc;">No components found.</div>';
                return;
            }

            container.innerHTML = components.map(component => `
                <div class="component-card">
                    <div style="display: flex; justify-content: space-between; align-items: flex-start;">
                        <div style="flex: 1;">
                            <h3 style="margin: 0; color: #00ffff;">${component.component_name}</h3>
                            <div style="margin: 5px 0; color: #ccc;">
                                <strong>Brand:</strong> ${component.brand || 'N/A'} |
                                <strong>Model:</strong> ${component.model || 'N/A'} |
                                <strong>Category:</strong> ${component.category_name}
                            </div>
                            <div class="component-specs">
                                ${component.specifications ? Object.entries(component.specifications).map(([key, value]) =>
                                    `<div class="spec-item"><strong>${key}:</strong> ${value}</div>`
                                ).join('') : '<div class="spec-item">No specifications</div>'}
                            </div>
                            <div style="margin-top: 10px;">
                                <span class="price-display">$${parseFloat(component.current_price).toFixed(2)}</span>
                                ${component.base_price !== component.current_price ?
                                    `<span style="text-decoration: line-through; color: #ccc; margin-left: 10px;">$${parseFloat(component.base_price).toFixed(2)}</span>` : ''
                                }
                                <span style="margin-left: 15px; color: #888;">Stock: ${component.stock_quantity}</span>
                                ${!component.is_active ? '<span style="margin-left: 15px; color: #ff6b6b;">INACTIVE</span>' : ''}
                            </div>
                        </div>
                        <div style="margin-left: 20px;">
                            <button class="btn btn-warning" onclick="editComponent(${component.id})">Edit</button>
                            <button class="btn btn-danger" onclick="deleteComponent(${component.id})">Delete</button>
                        </div>
                    </div>
                </div>
            `).join('');
        }

        // Load prebuilt configs
        function loadPrebuiltConfigs() {
            fetch('pc_components_api.php?action=get_prebuilt_configs&active_only=0', {
                credentials: 'same-origin'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    prebuiltConfigs = data.configs;
                    displayPrebuiltConfigs();
                    updateStatistics();
                }
            })
            .catch(error => console.error('Error loading prebuilt configs:', error));
        }

        function displayPrebuiltConfigs() {
            const container = document.getElementById('prebuilt-list');
            if (prebuiltConfigs.length === 0) {
                container.innerHTML = '<div style="text-align: center; padding: 40px; color: #ccc;">No pre-built configurations found.</div>';
                return;
            }

            container.innerHTML = prebuiltConfigs.map(config => `
                <div class="prebuilt-card">
                    <div style="display: flex; justify-content: space-between; align-items: flex-start;">
                        <div style="flex: 1;">
                            <h3 style="margin: 0; color: #ffd700;">${config.config_name}</h3>
                            <div style="margin: 5px 0; color: #ccc;">
                                <strong>Tier:</strong> ${config.tier.charAt(0).toUpperCase() + config.tier.slice(1)} |
                                <strong>Use:</strong> ${config.primary_use.replace('_', ' ')} |
                                ${!config.is_active ? '<span style="color: #ff6b6b;">INACTIVE</span>' : '<span style="color: #00ff00;">ACTIVE</span>'}
                            </div>
                            <div class="component-specs">
                                ${config.specifications_summary ? Object.entries(config.specifications_summary).map(([key, value]) =>
                                    `<div class="spec-item"><strong>${key}:</strong> ${value}</div>`
                                ).join('') : '<div class="spec-item">No specifications summary</div>'}
                            </div>
                            <div style="margin-top: 10px;">
                                <span class="price-display">$${parseFloat(config.current_price).toFixed(2)}</span>
                                ${config.discount_percentage > 0 ?
                                    `<span class="discount-badge">${config.discount_percentage.toFixed(1)}% OFF</span>` : ''
                                }
                                ${config.base_price !== config.current_price ?
                                    `<span style="text-decoration: line-through; color: #ccc; margin-left: 10px;">$${parseFloat(config.base_price).toFixed(2)}</span>` : ''
                                }
                            </div>
                            <div style="margin-top: 10px; font-size: 12px; color: #ccc;">
                                ${config.description || 'No description'}
                            </div>
                        </div>
                        <div style="margin-left: 20px;">
                            <button class="btn btn-warning" onclick="editPrebuiltConfig(${config.id})">Edit</button>
                            <button class="btn btn-danger" onclick="deletePrebuiltConfig(${config.id})">Delete</button>
                        </div>
                    </div>
                </div>
            `).join('');
        }

        // Load PC orders
        function loadPCOrders() {
            fetch('pc_components_api.php?action=admin_get_pc_orders', {
                credentials: 'same-origin'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    pcOrders = data.orders;
                    displayPCOrders();
                    updateStatistics();
                }
            })
            .catch(error => console.error('Error loading PC orders:', error));
        }

        function displayPCOrders() {
            const container = document.getElementById('orders-list');
            if (pcOrders.length === 0) {
                container.innerHTML = '<div style="text-align: center; padding: 40px; color: #ccc;">No PC orders found.</div>';
                return;
            }

            container.innerHTML = `
                <table class="table">
                    <thead>
                        <tr>
                            <th>Order #</th>
                            <th>Customer</th>
                            <th>Type</th>
                            <th>Status</th>
                            <th>Price</th>
                            <th>Date</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${pcOrders.map(order => `
                            <tr>
                                <td>${order.order_number}</td>
                                <td>${order.username}<br><small style="color: #ccc;">${order.email}</small></td>
                                <td>${order.order_type.charAt(0).toUpperCase() + order.order_type.slice(1)}</td>
                                <td>
                                    <span style="color: ${getStatusColor(order.status)};">
                                        ${order.status.charAt(0).toUpperCase() + order.status.slice(1)}
                                    </span>
                                </td>
                                <td>
                                    ${order.final_price ? '$' + parseFloat(order.final_price).toFixed(2) :
                                      order.estimated_price ? '$' + parseFloat(order.estimated_price).toFixed(2) + ' (est.)' : 'TBD'}
                                </td>
                                <td>${new Date(order.created_at).toLocaleDateString()}</td>
                                <td>
                                    <button class="btn btn-primary" onclick="viewPCOrder(${order.id})">View</button>
                                    <button class="btn btn-warning" onclick="editPCOrder(${order.id})">Edit</button>
                                </td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            `;
        }

        function getStatusColor(status) {
            const colors = {
                'pending': '#ffc107',
                'quoted': '#17a2b8',
                'confirmed': '#007bff',
                'processing': '#fd7e14',
                'completed': '#28a745',
                'cancelled': '#dc3545',
                'refunded': '#6c757d'
            };
            return colors[status] || '#ccc';
        }

        function updateStatistics() {
            document.getElementById('total-components').textContent = components.length;
            document.getElementById('total-prebuilt').textContent = prebuiltConfigs.length;
            document.getElementById('total-orders').textContent = pcOrders.length;
            document.getElementById('pending-orders').textContent = pcOrders.filter(order => order.status === 'pending').length;
        }

        // Search and filter functions
        function searchComponents() {
            const searchTerm = document.getElementById('component-search').value.toLowerCase();
            const filteredComponents = components.filter(component =>
                component.component_name.toLowerCase().includes(searchTerm) ||
                component.brand.toLowerCase().includes(searchTerm) ||
                component.model.toLowerCase().includes(searchTerm)
            );
            displayFilteredComponents(filteredComponents);
        }

        function filterComponents() {
            const categoryId = document.getElementById('category-filter').value;
            const filteredComponents = categoryId ?
                components.filter(component => component.category_id == categoryId) :
                components;
            displayFilteredComponents(filteredComponents);
        }

        function displayFilteredComponents(filteredComponents) {
            // Use the same display logic but with filtered data
            const originalComponents = components;
            components = filteredComponents;
            displayComponents();
            components = originalComponents;
        }

        // Modal functions (placeholders)
        function openAddComponentModal() {
            alert('Add Component modal - To be implemented');
        }

        function editComponent(id) {
            alert('Edit Component ' + id + ' - To be implemented');
        }

        function deleteComponent(id) {
            if (confirm('Are you sure you want to delete this component?')) {
                // Delete component logic
                alert('Delete Component ' + id + ' - To be implemented');
            }
        }

        function openAddPrebuiltModal() {
            alert('Add Pre-built Config modal - To be implemented');
        }

        function editPrebuiltConfig(id) {
            alert('Edit Pre-built Config ' + id + ' - To be implemented');
        }

        function deletePrebuiltConfig(id) {
            if (confirm('Are you sure you want to delete this pre-built configuration?')) {
                alert('Delete Pre-built Config ' + id + ' - To be implemented');
            }
        }

        function viewPCOrder(id) {
            alert('View PC Order ' + id + ' - To be implemented');
        }

        function editPCOrder(id) {
            alert('Edit PC Order ' + id + ' - To be implemented');
        }

        function openAddCategoryModal() {
            alert('Add Category modal - To be implemented');
        }

        function editCategory(id) {
            alert('Edit Category ' + id + ' - To be implemented');
        }

        function deleteCategory(id) {
            if (confirm('Are you sure you want to delete this category?')) {
                alert('Delete Category ' + id + ' - To be implemented');
            }
        }
    </script>
</body>
</html>
