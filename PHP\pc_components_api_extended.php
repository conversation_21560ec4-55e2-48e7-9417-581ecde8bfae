<?php
// Extended PC Components API Functions
// This file contains additional functions for the PC Components API

/**
 * Add prebuilt configuration
 */
function addPrebuiltConfig() {
    $local_link = get_db_connection();
    
    $config_name = $_POST['config_name'] ?? '';
    $config_name_en = $_POST['config_name_en'] ?? $config_name;
    $config_name_zh = $_POST['config_name_zh'] ?? $config_name;
    $tier = $_POST['tier'] ?? '';
    $primary_use = $_POST['primary_use'] ?? '';
    $components = $_POST['components'] ?? '{}';
    $base_price = floatval($_POST['base_price'] ?? 0);
    $current_price = floatval($_POST['current_price'] ?? $base_price);
    $discount_percentage = floatval($_POST['discount_percentage'] ?? 0);
    $description = $_POST['description'] ?? '';
    $description_en = $_POST['description_en'] ?? $description;
    $description_zh = $_POST['description_zh'] ?? $description;
    $specifications_summary = $_POST['specifications_summary'] ?? '{}';
    $is_active = isset($_POST['is_active']) ? 1 : 0;
    $sort_order = intval($_POST['sort_order'] ?? 0);
    
    if (!$config_name || !$tier || !$primary_use || $base_price < 0) {
        echo json_encode(['success' => false, 'message' => 'Invalid input data']);
        close_db_connection($local_link);
        return;
    }
    
    // Validate JSON fields
    if (!json_decode($components)) {
        $components = '{}';
    }
    if (!json_decode($specifications_summary)) {
        $specifications_summary = '{}';
    }
    
    $sql = "INSERT INTO pc_prebuilt_configs 
            (config_name, config_name_en, config_name_zh, tier, primary_use, components,
             base_price, current_price, discount_percentage, description, description_en, 
             description_zh, specifications_summary, is_active, sort_order)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
    
    $stmt = execute_query($local_link, $sql, "ssssssdddssssii", [
        $config_name, $config_name_en, $config_name_zh, $tier, $primary_use, $components,
        $base_price, $current_price, $discount_percentage, $description, $description_en,
        $description_zh, $specifications_summary, $is_active, $sort_order
    ]);
    
    if ($stmt) {
        mysqli_stmt_close($stmt);
        echo json_encode(['success' => true, 'message' => 'Prebuilt config added successfully']);
    } else {
        echo json_encode(['success' => false, 'message' => 'Failed to add prebuilt config']);
    }
    
    close_db_connection($local_link);
}

/**
 * Update prebuilt configuration
 */
function updatePrebuiltConfig() {
    $local_link = get_db_connection();
    
    $id = $_POST['id'] ?? 0;
    if (!$id) {
        echo json_encode(['success' => false, 'message' => 'Config ID required']);
        close_db_connection($local_link);
        return;
    }
    
    $config_name = $_POST['config_name'] ?? '';
    $config_name_en = $_POST['config_name_en'] ?? '';
    $config_name_zh = $_POST['config_name_zh'] ?? '';
    $tier = $_POST['tier'] ?? '';
    $primary_use = $_POST['primary_use'] ?? '';
    $components = $_POST['components'] ?? '{}';
    $base_price = floatval($_POST['base_price'] ?? 0);
    $current_price = floatval($_POST['current_price'] ?? 0);
    $discount_percentage = floatval($_POST['discount_percentage'] ?? 0);
    $description = $_POST['description'] ?? '';
    $description_en = $_POST['description_en'] ?? '';
    $description_zh = $_POST['description_zh'] ?? '';
    $specifications_summary = $_POST['specifications_summary'] ?? '{}';
    $is_active = isset($_POST['is_active']) ? 1 : 0;
    $sort_order = intval($_POST['sort_order'] ?? 0);
    
    // Validate JSON fields
    if (!json_decode($components)) {
        $components = '{}';
    }
    if (!json_decode($specifications_summary)) {
        $specifications_summary = '{}';
    }
    
    $sql = "UPDATE pc_prebuilt_configs SET 
            config_name = ?, config_name_en = ?, config_name_zh = ?, tier = ?, primary_use = ?,
            components = ?, base_price = ?, current_price = ?, discount_percentage = ?,
            description = ?, description_en = ?, description_zh = ?, specifications_summary = ?,
            is_active = ?, sort_order = ?
            WHERE id = ?";
    
    $stmt = execute_query($local_link, $sql, "ssssssdddssssiii", [
        $config_name, $config_name_en, $config_name_zh, $tier, $primary_use, $components,
        $base_price, $current_price, $discount_percentage, $description, $description_en,
        $description_zh, $specifications_summary, $is_active, $sort_order, $id
    ]);
    
    if ($stmt) {
        mysqli_stmt_close($stmt);
        echo json_encode(['success' => true, 'message' => 'Prebuilt config updated successfully']);
    } else {
        echo json_encode(['success' => false, 'message' => 'Failed to update prebuilt config']);
    }
    
    close_db_connection($local_link);
}

/**
 * Delete prebuilt configuration
 */
function deletePrebuiltConfig() {
    $local_link = get_db_connection();
    
    $id = $_POST['id'] ?? 0;
    if (!$id) {
        echo json_encode(['success' => false, 'message' => 'Config ID required']);
        close_db_connection($local_link);
        return;
    }
    
    $sql = "DELETE FROM pc_prebuilt_configs WHERE id = ?";
    $stmt = execute_query($local_link, $sql, "i", [$id]);
    
    if ($stmt) {
        mysqli_stmt_close($stmt);
        echo json_encode(['success' => true, 'message' => 'Prebuilt config deleted successfully']);
    } else {
        echo json_encode(['success' => false, 'message' => 'Failed to delete prebuilt config']);
    }
    
    close_db_connection($local_link);
}

/**
 * Create PC order
 */
function createPCOrder() {
    $local_link = get_db_connection();
    
    $user_id = $_SESSION['id'] ?? 0;
    $order_type = $_POST['order_type'] ?? '';
    $configuration = $_POST['configuration'] ?? '{}';
    $components = $_POST['components'] ?? null;
    $prebuilt_config_id = $_POST['prebuilt_config_id'] ?? null;
    $notes = $_POST['notes'] ?? '';
    
    if (!$user_id || !$order_type) {
        echo json_encode(['success' => false, 'message' => 'Invalid order data']);
        close_db_connection($local_link);
        return;
    }
    
    // Generate unique order number
    $order_number = 'PC' . date('Ymd') . sprintf('%04d', rand(1000, 9999));
    
    // Validate JSON fields
    if (!json_decode($configuration)) {
        $configuration = '{}';
    }
    if ($components && !json_decode($components)) {
        $components = null;
    }
    
    $sql = "INSERT INTO pc_orders 
            (order_number, user_id, order_type, configuration, components, prebuilt_config_id, notes)
            VALUES (?, ?, ?, ?, ?, ?, ?)";
    
    $stmt = execute_query($local_link, $sql, "sisssiss", [
        $order_number, $user_id, $order_type, $configuration, $components, $prebuilt_config_id, $notes
    ]);
    
    if ($stmt) {
        mysqli_stmt_close($stmt);
        echo json_encode(['success' => true, 'message' => 'PC order created successfully', 'order_number' => $order_number]);
    } else {
        echo json_encode(['success' => false, 'message' => 'Failed to create PC order']);
    }
    
    close_db_connection($local_link);
}

/**
 * Get user's PC orders
 */
function getPCOrders() {
    $local_link = get_db_connection();
    
    $user_id = $_SESSION['id'] ?? 0;
    if (!$user_id) {
        echo json_encode(['success' => false, 'message' => 'User not logged in']);
        close_db_connection($local_link);
        return;
    }
    
    $sql = "SELECT po.*, pbc.config_name, pbc.config_name_en, pbc.config_name_zh 
            FROM pc_orders po 
            LEFT JOIN pc_prebuilt_configs pbc ON po.prebuilt_config_id = pbc.id 
            WHERE po.user_id = ? 
            ORDER BY po.created_at DESC";
    
    $stmt = execute_query($local_link, $sql, "i", [$user_id]);
    
    if ($stmt) {
        $result = mysqli_stmt_get_result($stmt);
        $orders = [];
        while ($row = mysqli_fetch_assoc($result)) {
            // Decode JSON fields
            if ($row['configuration']) {
                $row['configuration'] = json_decode($row['configuration'], true);
            }
            if ($row['components']) {
                $row['components'] = json_decode($row['components'], true);
            }
            $orders[] = $row;
        }
        mysqli_stmt_close($stmt);
        echo json_encode(['success' => true, 'orders' => $orders]);
    } else {
        echo json_encode(['success' => false, 'message' => 'Failed to fetch PC orders']);
    }
    
    close_db_connection($local_link);
}

/**
 * Admin get all PC orders
 */
function adminGetPCOrders() {
    $local_link = get_db_connection();
    
    $sql = "SELECT po.*, u.username, u.first_name, u.last_name, u.email,
                   pbc.config_name, pbc.config_name_en, pbc.config_name_zh 
            FROM pc_orders po 
            JOIN users u ON po.user_id = u.id 
            LEFT JOIN pc_prebuilt_configs pbc ON po.prebuilt_config_id = pbc.id 
            ORDER BY po.created_at DESC";
    
    $result = mysqli_query($local_link, $sql);
    
    if ($result) {
        $orders = [];
        while ($row = mysqli_fetch_assoc($result)) {
            // Decode JSON fields
            if ($row['configuration']) {
                $row['configuration'] = json_decode($row['configuration'], true);
            }
            if ($row['components']) {
                $row['components'] = json_decode($row['components'], true);
            }
            $orders[] = $row;
        }
        echo json_encode(['success' => true, 'orders' => $orders]);
    } else {
        echo json_encode(['success' => false, 'message' => 'Failed to fetch PC orders']);
    }
    
    close_db_connection($local_link);
}

/**
 * Admin update PC order
 */
function adminUpdatePCOrder() {
    $local_link = get_db_connection();
    
    $id = $_POST['id'] ?? 0;
    if (!$id) {
        echo json_encode(['success' => false, 'message' => 'Order ID required']);
        close_db_connection($local_link);
        return;
    }
    
    $estimated_price = $_POST['estimated_price'] ?? null;
    $final_price = $_POST['final_price'] ?? null;
    $admin_adjusted_price = $_POST['admin_adjusted_price'] ?? null;
    $admin_adjustment_reason = $_POST['admin_adjustment_reason'] ?? '';
    $status = $_POST['status'] ?? '';
    $payment_status = $_POST['payment_status'] ?? '';
    $admin_notes = $_POST['admin_notes'] ?? '';
    $estimated_completion_date = $_POST['estimated_completion_date'] ?? null;
    
    $sql = "UPDATE pc_orders SET ";
    $updates = [];
    $params = [];
    $types = "";
    
    if ($estimated_price !== null) {
        $updates[] = "estimated_price = ?";
        $params[] = floatval($estimated_price);
        $types .= "d";
    }
    
    if ($final_price !== null) {
        $updates[] = "final_price = ?";
        $params[] = floatval($final_price);
        $types .= "d";
    }
    
    if ($admin_adjusted_price !== null) {
        $updates[] = "admin_adjusted_price = ?";
        $params[] = floatval($admin_adjusted_price);
        $types .= "d";
    }
    
    if ($admin_adjustment_reason) {
        $updates[] = "admin_adjustment_reason = ?";
        $params[] = $admin_adjustment_reason;
        $types .= "s";
    }
    
    if ($status) {
        $updates[] = "status = ?";
        $params[] = $status;
        $types .= "s";
    }
    
    if ($payment_status) {
        $updates[] = "payment_status = ?";
        $params[] = $payment_status;
        $types .= "s";
    }
    
    if ($admin_notes) {
        $updates[] = "admin_notes = ?";
        $params[] = $admin_notes;
        $types .= "s";
    }
    
    if ($estimated_completion_date) {
        $updates[] = "estimated_completion_date = ?";
        $params[] = $estimated_completion_date;
        $types .= "s";
    }
    
    if (empty($updates)) {
        echo json_encode(['success' => false, 'message' => 'No updates provided']);
        close_db_connection($local_link);
        return;
    }
    
    $sql .= implode(", ", $updates) . " WHERE id = ?";
    $params[] = $id;
    $types .= "i";
    
    $stmt = execute_query($local_link, $sql, $types, $params);
    
    if ($stmt) {
        mysqli_stmt_close($stmt);
        echo json_encode(['success' => true, 'message' => 'PC order updated successfully']);
    } else {
        echo json_encode(['success' => false, 'message' => 'Failed to update PC order']);
    }
    
    close_db_connection($local_link);
}
?>
