<?php
// Setup PC Components System
// This script creates the necessary database tables and inserts sample data

require_once 'config.php';

// Check if user is admin
session_start();
if (!isset($_SESSION['loggedin']) || $_SESSION['loggedin'] !== true || !isset($_SESSION['is_admin']) || $_SESSION['is_admin'] !== true) {
    die('Admin access required');
}

echo "<h1>Setting up PC Components System...</h1>";

// Read and execute SQL files
$sqlFiles = [
    '../SQL/setup_pc_components.sql',
    '../SQL/insert_sample_prebuilt_configs.sql'
];

foreach ($sqlFiles as $sqlFile) {
    if (file_exists($sqlFile)) {
        echo "<h2>Executing: " . basename($sqlFile) . "</h2>";
        
        $sql = file_get_contents($sqlFile);
        
        // Split SQL into individual statements
        $statements = array_filter(array_map('trim', explode(';', $sql)));
        
        foreach ($statements as $statement) {
            if (!empty($statement) && !preg_match('/^--/', $statement)) {
                try {
                    $result = mysqli_query($link, $statement);
                    if ($result) {
                        // Check if it's a SELECT statement that returns a message
                        if (stripos($statement, 'SELECT') === 0) {
                            $row = mysqli_fetch_assoc($result);
                            if ($row && isset($row['message'])) {
                                echo "<p style='color: green;'>✓ " . $row['message'] . "</p>";
                            }
                        } else {
                            echo "<p style='color: green;'>✓ Statement executed successfully</p>";
                        }
                    } else {
                        echo "<p style='color: red;'>✗ Error: " . mysqli_error($link) . "</p>";
                        echo "<p style='color: #666;'>Statement: " . substr($statement, 0, 100) . "...</p>";
                    }
                } catch (Exception $e) {
                    echo "<p style='color: red;'>✗ Exception: " . $e->getMessage() . "</p>";
                }
            }
        }
    } else {
        echo "<p style='color: orange;'>⚠ File not found: $sqlFile</p>";
    }
}

// Verify tables were created
echo "<h2>Verifying Tables...</h2>";

$tables = [
    'pc_component_categories',
    'pc_components', 
    'pc_prebuilt_configs',
    'pc_orders'
];

foreach ($tables as $table) {
    $result = mysqli_query($link, "SHOW TABLES LIKE '$table'");
    if (mysqli_num_rows($result) > 0) {
        echo "<p style='color: green;'>✓ Table '$table' exists</p>";
        
        // Show row count
        $countResult = mysqli_query($link, "SELECT COUNT(*) as count FROM $table");
        $count = mysqli_fetch_assoc($countResult)['count'];
        echo "<p style='color: blue;'>  → Contains $count rows</p>";
    } else {
        echo "<p style='color: red;'>✗ Table '$table' not found</p>";
    }
}

echo "<h2>Setup Complete!</h2>";
echo "<p><a href='admin_pc_management.php'>Go to PC Management</a></p>";
echo "<p><a href='admin.php'>Back to Admin Panel</a></p>";

// Close connection
mysqli_close($link);
?>
