-- PC Components Database Tables
-- These tables are needed for the PC Builder functionality

USE kelvinkms;

-- PC Component Categories Table
CREATE TABLE IF NOT EXISTS pc_component_categories (
    id INT AUTO_INCREMENT PRIMARY KEY,
    category_name VARCHAR(100) NOT NULL,
    category_name_en VARCHAR(100) NOT NULL,
    category_name_zh VARCHAR(100) NOT NULL,
    description TEXT,
    description_en TEXT,
    description_zh TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    sort_order INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_active (is_active),
    INDEX idx_sort (sort_order)
);

-- PC Components Table
CREATE TABLE IF NOT EXISTS pc_components (
    id INT AUTO_INCREMENT PRIMARY KEY,
    category_id INT NOT NULL,
    component_name VARCHAR(200) NOT NULL,
    component_name_en VARCHAR(200) NOT NULL,
    component_name_zh VARCHAR(200) NOT NULL,
    brand VARCHAR(100),
    model VARCHAR(100),
    specifications JSON,
    base_price DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    current_price DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    stock_quantity INT DEFAULT 0,
    description TEXT,
    description_en TEXT,
    description_zh TEXT,
    image_url VARCHAR(255),
    is_active BOOLEAN DEFAULT TRUE,
    sort_order INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (category_id) REFERENCES pc_component_categories(id) ON DELETE CASCADE,
    INDEX idx_category (category_id),
    INDEX idx_active (is_active),
    INDEX idx_sort (sort_order),
    INDEX idx_price (current_price)
);

-- PC Prebuilt Configurations Table
CREATE TABLE IF NOT EXISTS pc_prebuilt_configs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    config_name VARCHAR(200) NOT NULL,
    config_name_en VARCHAR(200) NOT NULL,
    config_name_zh VARCHAR(200) NOT NULL,
    description TEXT,
    description_en TEXT,
    description_zh TEXT,
    tier ENUM('entry', 'mid', 'high', 'extreme') NOT NULL,
    primary_use ENUM('gaming', 'video_editing', 'both', 'general') NOT NULL,
    components JSON NOT NULL COMMENT 'JSON array of component IDs and quantities',
    specifications_summary JSON COMMENT 'Key specifications for display',
    base_price DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    current_price DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    discount_percentage DECIMAL(5,2) DEFAULT 0.00,
    is_active BOOLEAN DEFAULT TRUE,
    sort_order INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_tier (tier),
    INDEX idx_use (primary_use),
    INDEX idx_active (is_active),
    INDEX idx_sort (sort_order)
);

-- Insert default component categories
INSERT INTO pc_component_categories (category_name, category_name_en, category_name_zh, description, description_en, description_zh, sort_order) VALUES
('CPU', 'CPU', 'CPU', 'Central Processing Unit', 'Central Processing Unit', '中央处理器', 1),
('GPU', 'Graphics Card', '显卡', 'Graphics Processing Unit', 'Graphics Processing Unit', '图形处理器', 2),
('RAM', 'Memory', '内存', 'Random Access Memory', 'Random Access Memory', '随机存储器', 3),
('Storage', 'Storage', '存储', 'Storage Devices', 'Storage Devices', '存储设备', 4),
('Motherboard', 'Motherboard', '主板', 'Motherboard', 'Motherboard', '主板', 5),
('PSU', 'Power Supply', '电源', 'Power Supply Unit', 'Power Supply Unit', '电源供应器', 6),
('Case', 'Case', '机箱', 'PC Case', 'PC Case', '电脑机箱', 7),
('Cooling', 'Cooling', '散热', 'Cooling System', 'Cooling System', '散热系统', 8);

-- Insert sample components
INSERT INTO pc_components (category_id, component_name, component_name_en, component_name_zh, brand, model, specifications, base_price, current_price, description, description_en, description_zh, sort_order) VALUES
(1, 'Intel Core i5-12400F', 'Intel Core i5-12400F', 'Intel Core i5-12400F', 'Intel', 'Core i5-12400F', '{"cores": 6, "threads": 12, "base_clock": "2.5 GHz", "boost_clock": "4.4 GHz", "socket": "LGA1700"}', 199.99, 189.99, 'Mid-range gaming processor', 'Mid-range gaming processor', '中端游戏处理器', 1),
(1, 'AMD Ryzen 5 5600X', 'AMD Ryzen 5 5600X', 'AMD Ryzen 5 5600X', 'AMD', 'Ryzen 5 5600X', '{"cores": 6, "threads": 12, "base_clock": "3.7 GHz", "boost_clock": "4.6 GHz", "socket": "AM4"}', 299.99, 279.99, 'High-performance gaming processor', 'High-performance gaming processor', '高性能游戏处理器', 2),
(2, 'NVIDIA RTX 3060', 'NVIDIA RTX 3060', 'NVIDIA RTX 3060', 'NVIDIA', 'GeForce RTX 3060', '{"memory": "12GB GDDR6", "base_clock": "1320 MHz", "boost_clock": "1777 MHz", "ray_tracing": true}', 329.99, 299.99, 'Mid-range gaming graphics card', 'Mid-range gaming graphics card', '中端游戏显卡', 1),
(2, 'NVIDIA RTX 3070', 'NVIDIA RTX 3070', 'NVIDIA RTX 3070', 'NVIDIA', 'GeForce RTX 3070', '{"memory": "8GB GDDR6", "base_clock": "1500 MHz", "boost_clock": "1725 MHz", "ray_tracing": true}', 499.99, 479.99, 'High-performance gaming graphics card', 'High-performance gaming graphics card', '高性能游戏显卡', 2),
(3, 'Corsair Vengeance LPX 16GB', 'Corsair Vengeance LPX 16GB', 'Corsair Vengeance LPX 16GB', 'Corsair', 'Vengeance LPX', '{"capacity": "16GB", "speed": "3200MHz", "type": "DDR4", "modules": "2x8GB"}', 79.99, 69.99, 'High-performance gaming memory', 'High-performance gaming memory', '高性能游戏内存', 1),
(4, 'Samsung 970 EVO Plus 500GB', 'Samsung 970 EVO Plus 500GB', 'Samsung 970 EVO Plus 500GB', 'Samsung', '970 EVO Plus', '{"capacity": "500GB", "type": "NVMe SSD", "interface": "M.2", "read_speed": "3500 MB/s"}', 89.99, 79.99, 'High-speed NVMe SSD', 'High-speed NVMe SSD', '高速NVMe固态硬盘', 1);

-- Insert sample prebuilt configurations
INSERT INTO pc_prebuilt_configs (config_name, config_name_en, config_name_zh, description, description_en, description_zh, tier, primary_use, components, specifications_summary, base_price, current_price, discount_percentage, sort_order) VALUES
('Budget Gaming Build', 'Budget Gaming Build', '预算游戏配置', 'Perfect for 1080p gaming', 'Perfect for 1080p gaming', '完美的1080p游戏配置', 'entry', 'gaming', '{"cpu": 1, "gpu": 1, "ram": 1, "storage": 1}', '{"CPU": "Intel i5-12400F", "GPU": "RTX 3060", "RAM": "16GB DDR4", "Storage": "500GB NVMe SSD"}', 899.99, 799.99, 11.11, 1),
('High-End Gaming Build', 'High-End Gaming Build', '高端游戏配置', 'Excellent for 1440p gaming', 'Excellent for 1440p gaming', '出色的1440p游戏配置', 'high', 'gaming', '{"cpu": 2, "gpu": 2, "ram": 1, "storage": 1}', '{"CPU": "AMD Ryzen 5 5600X", "GPU": "RTX 3070", "RAM": "16GB DDR4", "Storage": "500GB NVMe SSD"}', 1299.99, 1199.99, 7.69, 2);