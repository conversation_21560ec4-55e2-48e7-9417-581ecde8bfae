-- =============================================
-- PC Components Management System Setup
-- =============================================

USE kelvinkms;

-- PC Component Categories Table
CREATE TABLE IF NOT EXISTS pc_component_categories (
    id INT AUTO_INCREMENT PRIMARY KEY,
    category_name VARCHAR(50) NOT NULL UNIQUE,
    category_name_en VARCHAR(50) NOT NULL,
    category_name_zh VARCHAR(50) NOT NULL,
    description TEXT,
    sort_order INT DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_category_name (category_name),
    INDEX idx_active (is_active),
    INDEX idx_sort (sort_order)
);

-- PC Components Table
CREATE TABLE IF NOT EXISTS pc_components (
    id INT AUTO_INCREMENT PRIMARY KEY,
    category_id INT NOT NULL,
    component_name VARCHAR(200) NOT NULL,
    component_name_en VARCHAR(200) NOT NULL,
    component_name_zh VARCHAR(200) NOT NULL,
    brand VARCHAR(100),
    model VARCHAR(100),
    specifications JSON COMMENT 'Component specifications in JSON format',
    base_price DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    current_price DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    stock_quantity INT DEFAULT 0,
    min_stock_level INT DEFAULT 0,
    image_url VARCHAR(500),
    description TEXT,
    description_en TEXT,
    description_zh TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    is_featured BOOLEAN DEFAULT FALSE,
    sort_order INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (category_id) REFERENCES pc_component_categories(id) ON DELETE CASCADE,
    INDEX idx_category_id (category_id),
    INDEX idx_brand (brand),
    INDEX idx_active (is_active),
    INDEX idx_featured (is_featured),
    INDEX idx_price (current_price),
    INDEX idx_sort (sort_order)
);

-- Pre-built PC Configurations Table
CREATE TABLE IF NOT EXISTS pc_prebuilt_configs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    config_name VARCHAR(200) NOT NULL,
    config_name_en VARCHAR(200) NOT NULL,
    config_name_zh VARCHAR(200) NOT NULL,
    tier ENUM('entry', 'mid', 'high', 'extreme') NOT NULL,
    primary_use ENUM('gaming', 'video_editing', 'both', 'general') NOT NULL,
    components JSON NOT NULL COMMENT 'Selected components with IDs and quantities',
    base_price DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    current_price DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    discount_percentage DECIMAL(5,2) DEFAULT 0.00,
    image_url VARCHAR(500),
    description TEXT,
    description_en TEXT,
    description_zh TEXT,
    specifications_summary JSON COMMENT 'Summary of key specs for display',
    is_active BOOLEAN DEFAULT TRUE,
    is_featured BOOLEAN DEFAULT FALSE,
    sort_order INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_tier (tier),
    INDEX idx_use (primary_use),
    INDEX idx_active (is_active),
    INDEX idx_featured (is_featured),
    INDEX idx_price (current_price),
    INDEX idx_sort (sort_order)
);

-- PC Orders Table (Enhanced for component selection)
CREATE TABLE IF NOT EXISTS pc_orders (
    id INT AUTO_INCREMENT PRIMARY KEY,
    order_number VARCHAR(50) UNIQUE NOT NULL,
    user_id INT NOT NULL,
    order_type ENUM('simple', 'detailed', 'prebuilt') NOT NULL,
    configuration JSON NOT NULL COMMENT 'Selected configuration details',
    components JSON COMMENT 'Selected components for detailed mode',
    prebuilt_config_id INT DEFAULT NULL COMMENT 'Reference to prebuilt config if applicable',
    estimated_price DECIMAL(10,2) DEFAULT NULL,
    final_price DECIMAL(10,2) DEFAULT NULL,
    admin_adjusted_price DECIMAL(10,2) DEFAULT NULL,
    admin_adjustment_reason TEXT,
    status ENUM('pending', 'quoted', 'confirmed', 'processing', 'completed', 'cancelled', 'refunded') DEFAULT 'pending',
    payment_status ENUM('pending', 'paid', 'partially_paid', 'refunded', 'failed') DEFAULT 'pending',
    payment_method VARCHAR(50) DEFAULT NULL,
    notes TEXT,
    admin_notes TEXT,
    estimated_completion_date DATE DEFAULT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (prebuilt_config_id) REFERENCES pc_prebuilt_configs(id) ON DELETE SET NULL,
    INDEX idx_user_id (user_id),
    INDEX idx_order_type (order_type),
    INDEX idx_status (status),
    INDEX idx_payment_status (payment_status),
    INDEX idx_created_at (created_at)
);

-- Insert initial component categories
INSERT INTO pc_component_categories (category_name, category_name_en, category_name_zh, description, sort_order) VALUES
('cpu', 'CPU', '處理器', 'Central Processing Unit', 1),
('gpu', 'GPU', '顯示卡', 'Graphics Processing Unit', 2),
('ram', 'RAM', '記憶體', 'Random Access Memory', 3),
('storage', 'Storage', '儲存裝置', 'Storage Devices (SSD/HDD)', 4),
('motherboard', 'Motherboard', '主機板', 'Motherboard', 5),
('psu', 'PSU', '電源供應器', 'Power Supply Unit', 6),
('case', 'Case', '機殼', 'PC Case', 7),
('cooling', 'Cooling', '散熱系統', 'Cooling System', 8),
('accessories', 'Accessories', '配件', 'Additional Accessories', 9);

-- Insert sample components for demonstration
INSERT INTO pc_components (category_id, component_name, component_name_en, component_name_zh, brand, model, specifications, base_price, current_price, stock_quantity, description, description_en, description_zh, sort_order) VALUES
-- CPUs
(1, 'Intel Core i5-13600K', 'Intel Core i5-13600K', 'Intel Core i5-13600K', 'Intel', 'i5-13600K', '{"cores": 14, "threads": 20, "base_clock": "3.5GHz", "boost_clock": "5.1GHz", "socket": "LGA1700"}', 329.99, 329.99, 10, 'High-performance gaming CPU', 'High-performance gaming CPU', '高性能遊戲處理器', 1),
(1, 'AMD Ryzen 7 7700X', 'AMD Ryzen 7 7700X', 'AMD Ryzen 7 7700X', 'AMD', '7700X', '{"cores": 8, "threads": 16, "base_clock": "4.5GHz", "boost_clock": "5.4GHz", "socket": "AM5"}', 399.99, 399.99, 8, 'Excellent for gaming and content creation', 'Excellent for gaming and content creation', '遊戲和內容創作的絕佳選擇', 2),

-- GPUs
(2, 'NVIDIA RTX 4070', 'NVIDIA RTX 4070', 'NVIDIA RTX 4070', 'NVIDIA', 'RTX 4070', '{"memory": "12GB GDDR6X", "memory_bus": "192-bit", "boost_clock": "2610MHz", "ray_tracing": true}', 599.99, 599.99, 5, 'Excellent 1440p gaming performance', 'Excellent 1440p gaming performance', '優秀的1440p遊戲性能', 1),
(2, 'AMD RX 7800 XT', 'AMD RX 7800 XT', 'AMD RX 7800 XT', 'AMD', 'RX 7800 XT', '{"memory": "16GB GDDR6", "memory_bus": "256-bit", "boost_clock": "2430MHz", "ray_tracing": true}', 549.99, 549.99, 6, 'Great value for high-end gaming', 'Great value for high-end gaming', '高端遊戲的超值選擇', 2),

-- RAM
(3, 'Corsair Vengeance LPX 32GB', 'Corsair Vengeance LPX 32GB', 'Corsair Vengeance LPX 32GB', 'Corsair', 'CMK32GX4M2E3200C16', '{"capacity": "32GB", "speed": "DDR4-3200", "timings": "16-18-18-36", "voltage": "1.35V"}', 129.99, 129.99, 15, 'High-performance DDR4 memory', 'High-performance DDR4 memory', '高性能DDR4記憶體', 1),
(3, 'G.Skill Trident Z5 32GB', 'G.Skill Trident Z5 32GB', 'G.Skill Trident Z5 32GB', 'G.Skill', 'F5-6000J3636F16GX2-TZ5K', '{"capacity": "32GB", "speed": "DDR5-6000", "timings": "36-36-36-96", "voltage": "1.35V"}', 199.99, 199.99, 12, 'Latest DDR5 technology', 'Latest DDR5 technology', '最新DDR5技術', 2);

SELECT 'PC Components tables setup completed successfully!' as message;
