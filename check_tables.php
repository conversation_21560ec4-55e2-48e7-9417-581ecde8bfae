<?php
require_once 'PHP/config.php';

echo "<!DOCTYPE html><html><head><title>Database Tables Check</title></head><body>";
echo "<h1>Database Tables Check</h1>";

$link = get_db_connection();

// Check all tables
$sql = "SHOW TABLES";
$result = mysqli_query($link, $sql);

if ($result) {
    echo "<h2>All Tables in Database:</h2>";
    echo "<ul>";
    while ($row = mysqli_fetch_array($result)) {
        echo "<li>" . $row[0] . "</li>";
    }
    echo "</ul>";
    
    // Check specifically for PC component tables
    $pc_tables = ['pc_component_categories', 'pc_components', 'pc_prebuilt_configs'];
    echo "<h2>PC Component Tables Status:</h2>";
    echo "<ul>";
    
    foreach ($pc_tables as $table) {
        $sql = "SHOW TABLES LIKE '$table'";
        $result = mysqli_query($link, $sql);
        if (mysqli_num_rows($result) > 0) {
            echo "<li style='color: green;'>✓ $table - EXISTS</li>";
        } else {
            echo "<li style='color: red;'>✗ $table - MISSING</li>";
        }
    }
    echo "</ul>";
} else {
    echo "<p style='color: red;'>Error checking tables: " . mysqli_error($link) . "</p>";
}

close_db_connection($link);
echo "</body></html>";
?>