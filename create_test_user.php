<?php
// Create Test User Account
require_once 'PHP/config.php';

// Basic security - only allow localhost access
if (!in_array($_SERVER['REMOTE_ADDR'], ['127.0.0.1', '::1'])) {
    die('Access denied. This script can only be run from localhost.');
}

echo "<!DOCTYPE html>
<html>
<head>
    <title>Create Test User</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
    </style>
</head>
<body>
    <h1>Create Test User Account</h1>";

// Get database connection
$link = get_db_connection();

if (!$link) {
    echo "<p class='error'>Database connection failed: " . mysqli_connect_error() . "</p>";
    exit;
}

// Test user data
$username = 'kelvin';
$email = '<EMAIL>';
$password_hash = '$2y$12$amKRzBxvT.yfKJqcBEoXRuVzx6L0EFX9OEyfwQxeQ58AgMcQncCfm'; // Hash for "k1e9l9v9in"
$first_name = 'Kelvin';
$last_name = 'Test';
$phone_number = '**********';

// Check if user already exists
$check_sql = "SELECT id FROM users WHERE username = ? OR email = ?";
$check_stmt = mysqli_prepare($link, $check_sql);
mysqli_stmt_bind_param($check_stmt, "ss", $username, $email);
mysqli_stmt_execute($check_stmt);
$result = mysqli_stmt_get_result($check_stmt);

if (mysqli_num_rows($result) > 0) {
    echo "<p class='info'>User already exists. Updating password...</p>";
    
    // Update existing user's password
    $update_sql = "UPDATE users SET password = ? WHERE username = ?";
    $update_stmt = mysqli_prepare($link, $update_sql);
    mysqli_stmt_bind_param($update_stmt, "ss", $password_hash, $username);
    
    if (mysqli_stmt_execute($update_stmt)) {
        echo "<p class='success'>✓ Password updated successfully!</p>";
    } else {
        echo "<p class='error'>✗ Error updating password: " . mysqli_error($link) . "</p>";
    }
    mysqli_stmt_close($update_stmt);
} else {
    echo "<p class='info'>Creating new user account...</p>";
    
    // Insert new user
    $insert_sql = "INSERT INTO users (username, email, password, first_name, last_name, phone_number, is_verified)
                   VALUES (?, ?, ?, ?, ?, ?, 1)";
    $insert_stmt = mysqli_prepare($link, $insert_sql);
    mysqli_stmt_bind_param($insert_stmt, "ssssss",
        $username, $email, $password_hash, $first_name, $last_name, $phone_number);
    
    if (mysqli_stmt_execute($insert_stmt)) {
        echo "<p class='success'>✓ Test user created successfully!</p>";
    } else {
        echo "<p class='error'>✗ Error creating user: " . mysqli_error($link) . "</p>";
    }
    mysqli_stmt_close($insert_stmt);
}

mysqli_stmt_close($check_stmt);
close_db_connection($link);

echo "<h2>Test User Details</h2>";
echo "<p><strong>Username:</strong> $username</p>";
echo "<p><strong>Password:</strong> k1e9l9v9in</p>";
echo "<p><strong>Email:</strong> $email</p>";
echo "<p class='info'>You can now use these credentials to log in and test the PC Builder functionality.</p>";

echo "</body></html>";
?>