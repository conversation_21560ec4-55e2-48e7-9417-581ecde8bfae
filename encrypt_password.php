<?php
// <PERSON><PERSON><PERSON> to encrypt a password using the same method as the registration system
$password = "k1e9l9v9in";

// Hash the password using PHP's password_hash() function with PASSWORD_DEFAULT
$hashed_password = password_hash($password, PASSWORD_DEFAULT);

echo "Original password: " . $password . "\n";
echo "Encrypted password: " . $hashed_password . "\n";
echo "\n";

// Verify the hash works correctly
if (password_verify($password, $hashed_password)) {
    echo "✓ Password verification successful - the hash is working correctly.\n";
} else {
    echo "✗ Password verification failed - there's an issue with the hash.\n";
}
?>