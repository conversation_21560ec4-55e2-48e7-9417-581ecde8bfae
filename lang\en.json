{"site_title": "KelvinKMS.com", "login_button": "<PERSON><PERSON>", "register_button": "Register", "contact_us": "Contact Us", "logout_button": "Logout", "page_views": "Page Views", "home_welcome": "Welcome to<br />KelvinKMS.com", "home_intro1": "Hello! My name is <PERSON><PERSON>.", "home_intro2": "I offer some services for you.", "home_service_list_title": "Service List:", "service_vip_pc": "VIP Custom PC", "service_optimize_media": "Optimize Photo & Video", "service_print": "Print Service", "service_consult": "Question Consult", "nav_home": "Home", "error_title_default": "Error", "success_title_default": "Success", "confirm_title_default": "Please Confirm", "loading_title_default": "Loading...", "create_account_title": "Create Account", "first_name": "First Name", "fist_name_placeholder": "Enter your first name", "last_name": "Last Name", "last_name_placeholder": "Enter your last name", "nickname": "Nickname", "nickname_placeholder": "Enter your nickname", "nickname_hint": "Nickname is required. Case-sensitive. Can include any language characters.", "username": "Username", "username_placeholder": "Enter your username", "username_hint_long": "Username is case-insensitive. First character cannot be a period. Use English letters, numbers, and periods. 6-20 characters. Cannot include admin, kelvinkms, gm.", "username_req_title": "Username must contain:", "username_req_length": "6-20 characters", "username_req_first_char": "First character cannot be a period", "username_req_allowed_chars": "Only letters, numbers, and periods allowed", "username_req_sensitive": "Cannot include admin, kelvinkms, gm", "gender": "Gender", "gender_male": "Male", "gender_female": "Female", "birthday": "Birthday", "year": "Year", "month": "Month", "day": "Day", "language": "Language", "lang_en": "English", "lang_zh": "中文", "lang_es": "Español", "lang_ja": "日本語", "lang_ko": "한국어", "lang_other": "Other", "email": "E-mail", "email_placeholder": "Enter your email address", "email_confirm": "Confirm E-mail", "email_confirm_placeholder": "Confirm your email address", "phone": "Phone Number", "phone_placeholder": "Enter your phone number", "send_code": "Send Code", "sms_code_placeholder": "Enter verification code", "verify_code": "Verify Code", "password": "Password", "password_placeholder": "Enter your password", "password_req_title": "Password must contain:", "password_req_length": "At least 10 characters", "password_req_uppercase": "Uppercase letter (A-Z)", "password_req_lowercase": "Lowercase letter (a-z)", "password_req_number": "Number (0-9)", "password_req_special": "Special character (!@#$%^&*...)", "password_confirm": "Confirm Password", "password_confirm_placeholder": "Confirm your password", "register_button_modal": "Register", "member_login_title": "Member <PERSON><PERSON>", "login_username_placeholder": "Username", "login_password_placeholder": "Password", "login_button_modal": "<PERSON><PERSON>", "js_form_invalid_name": "First name and last name are required.", "js_form_invalid_nickname": "Nickname is required.", "js_form_invalid_username": "Please check username requirements.", "js_form_invalid_gender": "Please select your gender.", "js_form_invalid_birthday": "Please enter your birthday.", "js_form_age_restriction": "You must be at least 13 years old to register.", "js_form_invalid_language": "Please select your preferred language.", "js_form_invalid_email": "Please enter a valid email address.", "js_form_email_mismatch": "Email addresses do not match.", "js_form_invalid_phone": "Phone number is required.", "js_form_phone_verification_required": "Please verify your phone number first.", "js_form_invalid_password": "Please check password requirements.", "js_form_password_mismatch": "Passwords do not match.", "js_form_creating_account": "Creating Account", "js_form_wait_creating_account": "Please wait while we create your account...", "js_form_registration_success_title": "Registration Successful", "js_form_registration_success_msg": "Your account has been created! Please login with your credentials.", "js_form_registration_failed_title": "Registration Failed", "js_form_registration_failed_msg": "Registration failed. Please try again.", "js_form_passwords_match": "Passwords match.", "js_form_passwords_nomatch": "Passwords do not match.", "js_form_email_match": "E-mail match.", "js_form_checking_username": "Checking username...", "js_form_username_taken": "Username is already taken.", "js_form_username_available": "Username is available.", "js_form_error_checking_username": "Error checking username.", "js_form_invalid_email_format": "Invalid email format.", "js_form_valid_email_format": "Valid email format.", "js_form_valid_birthday": "Valid birthday.", "js_form_enter_phone_first": "Enter your phone number first.", "js_form_code_sent": "Verification code sent.", "js_form_failed_to_send_code": "Failed to send code.", "js_form_enter_code": "Enter verification code.", "js_form_phone_verified": "Phone verified.", "js_form_incorrect_code": "Incorrect code.", "js_form_logging_in_title": "Logging In", "js_form_logging_in_msg": "Please wait while we verify your credentials...", "js_form_login_failed_title": "Login Failed", "js_form_invalid_credentials": "Invalid username or password.", "js_form_login_server_error": "The server returned an unexpected response. See details below.", "member_page_title": "Member Area - KelvinKMS.com", "welcome_message": "Welcome, {username}!", "service_orders_title": "Service Orders", "service_selection_prompt": "Please select the services you need and provide detailed information in the notes, then click to place your order.", "order_notes_placeholder": "Please enter your detailed requirements here", "confirm_order_button": "Confirm Order", "order_history_title": "Your Orders", "vip_pc_select_config_mode": "Select your configuration mode", "vip_pc_simple_mode_button": "Simple Mode", "vip_pc_detailed_mode_button": "Detailed Mode (Coming Soon)", "vip_pc_color_title": "Color", "vip_pc_color_white": "White", "vip_pc_color_black": "Black", "vip_pc_size_title": "Size", "vip_pc_size_large": "Large", "vip_pc_size_standard": "Standard", "vip_pc_size_small": "Small", "vip_pc_use_title": "Primary Use", "vip_pc_use_gaming": "Gaming", "vip_pc_use_video_editing": "Video Editing", "vip_pc_use_both": "Both", "vip_pc_tier_title": "Performance Tier", "vip_pc_tier_entry": "Entry", "vip_pc_tier_mid": "Mid-Range", "vip_pc_tier_high": "High-End", "vip_pc_tier_extreme": "Extreme", "js_error_no_service_title": "No Service Selected", "js_error_no_service_msg": "Please select at least one service before placing your order.", "js_error_config_required_title": "Configuration Required", "js_error_config_required_msg": "Please select a configuration mode for VIP Custom PC.", "js_error_all_options_required_title": "All Options Required", "js_error_all_options_required_msg": "Please select an option for each category in Simple Mode.", "js_confirm_order_title": "Confirm Order", "js_confirm_order_msg": "Are you sure you want to place this order for {count} service(s)?", "js_submitting_order_title": "Submitting Order", "js_submitting_order_msg": "Please wait while we process your order...", "js_order_success_title": "Order Submitted", "js_order_success_msg": "Your order has been submitted successfully! We will contact you soon.", "js_order_failed_title": "Order Failed", "js_order_failed_msg": "Failed to submit order. Please try again.", "js_network_error_msg": "Network error. Please check your connection and try again.", "js_confirm_logout_title": "Confirm <PERSON>ut", "js_confirm_logout_msg": "Are you sure you want to logout? You will need to login again to access your account.", "js_logging_out_title": "Logging Out", "js_logging_out_msg": "Please wait...", "js_no_orders_found": "No orders found.", "js_error_loading_orders": "Error loading orders.", "js_no_orders_yet": "You have not placed any orders yet.", "js_order_status": "Status", "js_order_status_pending": "Order Received", "js_order_status_processing": "Order in Progress", "js_order_status_completed": "Order Completed", "js_order_status_cancelled": "Order Cancelled", "js_estimated_price": "Estimated Price", "js_final_price": "Final Price", "js_services": "Services", "js_notes": "Notes", "js_cancel_order_button": "Cancel Order", "js_cancel_confirm_title": "Cancel Order", "js_cancel_confirm_msg": "Are you sure you want to cancel this order? This action cannot be undone.", "js_cancelling_order_title": "Cancelling Order", "js_cancelling_order_msg": "Please wait...", "js_cancel_success_title": "Order Cancelled", "js_cancel_success_msg": "Your order has been cancelled successfully.", "js_cancel_failed_title": "Cancellation Failed", "js_cancel_failed_msg": "Failed to cancel order.", "vip_pc_desc_1": "The most beautiful custom PC you can expect.", "vip_pc_desc_2": "I make all custom PCs one by one.", "vip_pc_desc_3": "Contact me for details.", "vip_pc_why_choose_title": "Why Choose My PC?", "vip_pc_reason_1_title": "🔧 Premium Component Quality", "vip_pc_reason_1_desc": "Compared to other PC brands, I use only the finest computer components. Every single part is carefully selected - I never compromise on quality to save costs or use inferior components.", "vip_pc_reason_2_title": "✨ Most Beautiful Aesthetics & Lighting", "vip_pc_reason_2_desc": "Unlike other brands that maximize profits by using cheap cases and RGB fans, I focus on creating the most beautiful appearance and stunning lighting effects.", "vip_pc_reason_3_title": "🎯 Complete Customization Freedom", "vip_pc_reason_3_desc": "Whether you buy from physical stores or other online PC retailers, you can hardly choose the exact components you want, resulting in suboptimal appearance and quality. I provide complete customization freedom.", "vip_pc_reason_4_title": "🛡️ Free 3-Year Warranty", "vip_pc_reason_4_desc": "I provide a free 3-year warranty that almost no other brand dares to offer - this is worth approximately $350. Other brands use poor-quality components that typically fail within 1-2 years, forcing you to buy a new PC after warranty expires, costing you much more. While I offer 3 years free warranty, with my 20+ years of PC building experience, my computers never break down and can last 10+ years. Buying one PC from me can save you thousands of dollars in replacement costs over a decade.", "vip_pc_reason_5_title": "⚙️ Professional System Installation & Optimization", "vip_pc_reason_5_desc": "Most other PC brands don't spend much time testing system stability, causing many customers to experience setup issues or even boot failures at home. This leads to frustrating hours on customer service calls or having to return/exchange units. My PCs go beyond just assembly - they include complete system installation, configuration, optimization, and testing to ensure customers can power on and use immediately upon delivery! This humanized experience is something only I can provide, elevating the entire service quality.", "vip_pc_mission_statement": "I simply want to create a channel for ordinary consumers who want to buy good computers but don't know where to go.", "pc_builder_title": "💻 PC Builder Configurator", "pc_builder_mode_selection": "Select Configuration Mode", "pc_builder_simple_mode": "Simple Mode", "pc_builder_detailed_mode": "Detailed Mode", "pc_builder_prebuilt_mode": "Pre-built Packages", "pc_builder_mode_description_simple": "Quick selection of basic configuration options", "pc_builder_mode_description_detailed": "Detailed selection of each component (like PCPartPicker)", "pc_builder_mode_description_prebuilt": "Choose from pre-configured PC packages", "pc_builder_admin_restriction": "Admin Restriction:", "pc_builder_restriction_none": "No Restrictions - All modes available", "pc_builder_restriction_simple_only": "Simple Mode Only", "pc_builder_restriction_detailed_only": "Detailed Mode Only", "pc_builder_restriction_prebuilt_only": "Pre-built Mode Only", "pc_builder_component_cpu": "Processor (CPU)", "pc_builder_component_gpu": "Graphics Card (GPU)", "pc_builder_component_ram": "Memory (RAM)", "pc_builder_component_storage": "Storage", "pc_builder_component_motherboard": "Motherboard", "pc_builder_component_psu": "Power Supply (PSU)", "pc_builder_component_case": "Case", "pc_builder_component_cooling": "Cooling System", "pc_builder_estimated_total": "Estimated Total", "pc_builder_request_quote": "Request Quote", "pc_builder_prebuilt_specs": "Basic Specifications", "pc_builder_prebuilt_performance": "Performance Level", "optimize_desc_1": "I can optimize your low quality photo or video to the best quality.", "optimize_desc_2": "I can remove photo or video watermark.", "optimize_price_photo": "One photo optimize = $3", "optimize_price_photo_watermark": "One photo watermark remove = $3", "optimize_price_photo_discount": "Over 10 photos = 10% discount", "optimize_price_video": "One video 1 min 30FPS 1080P = $2", "optimize_price_video_watermark_title": "Video Watermark Remove", "optimize_price_video_watermark_easy": "Easy mode = $10 each up to 30 mins", "optimize_price_video_watermark_hard": "Hard mode = $30 each up to 30 mins", "notice_price_change": "<strong>Notice :</strong> Price can be changed if I need to adjust.", "print_desc_1": "We offer print service", "print_desc_2": "You can print your documents or photos", "print_desc_3": "Very high quality", "print_paper_1_title": "(Letter size) 32 lb Premium Paper", "print_paper_1_price": "Black $0.5 , Color $1", "print_paper_2_title": "(Letter size) 110 lb Ultra Premium Paper", "print_paper_2_price": "Black $1 , Color $2", "print_paper_3_title": "(Letter size) Premium 45 lbs Photo Paper (Matte)", "print_paper_3_price": "Black $1.5 , Color $3", "print_paper_4_title": "(Letter size) Five Stars Premium 70 lbs Photo Paper (Glossy)", "print_paper_4_price": "Black $2.5 , Color $5", "print_paper_5_title": "4\\\"x 6\\\" Five Stars Premium 70 lbs Photo Paper", "print_paper_5_price": "Black & Color $1 each", "print_laminating_1_title": "(Letter size) 5 mil Thermal Laminating Pouch $5", "print_laminating_2_title": "4\\\"x6\\\" 5 mil Thermal Laminating Pouch $3", "print_price_include_fee": "Price includes service fee", "print_shipping_fee": "Shipping & handling fee = $3 ~ $10 based on volume and paper size", "print_discount": "10% discount applied when you print over 10 papers", "print_album_title": "<strong>Beautiful Photo Album for sale</strong>", "print_album_1": "Basic Photo Album 36 Photos = $5", "print_album_2": "Premium Hard Cover Pink Flower 52 Photos = $15 each", "print_album_3": "Premium Hard Cover Pink Flower 300 Photos = $35 each", "print_album_4": "Premium Hard Cover Pink Flower 600 Photos = $55 each", "print_privacy_title": "<strong>Privacy Policy</strong>", "print_privacy_1": "All your documents or photos we print will be deleted after the print job is completed.", "print_privacy_2": "We do not keep backup.", "consult_desc_1": "You can ask questions for the answers. Price vary depend on complexity. contact me for details.", "copyright_title": "All rights strictly reserved", "copyright_year": "Copyright 2025", "copyright_name": "KelvinKMS.com", "online_users": "Online Members: ", "live_chat_title": "Live Chat", "live_chat_welcome": "Please provide your info to start the chat.", "live_chat_start_button": "Start Chat", "live_chat_input_placeholder": "Type a message...", "pc_order_form_title": "Request a Quote for a Custom PC", "pc_order_form_placeholder": "Describe your dream PC here. Include desired components (CPU, GPU, RAM), primary use (e.g., gaming, video editing), budget, and any aesthetic preferences (e.g., color, lighting). The more details, the better!", "pc_order_form_submit": "Request Quote", "pc_order_login_prompt": "Please login or register to request a quote for a custom PC.", "admin_pc_orders_title": "Admin - PC Orders", "back_to_admin_panel": "Back to Admin Panel", "admin_pc_orders_header": "Manage Custom PC Orders", "order_id": "Order ID", "customer": "Customer", "order_details": "Order Details", "final_price": "Final Price", "eta": "Est<PERSON>", "order_date": "Order Date", "action": "Action", "pc_orders_history_title": "My Custom PC Orders", "no_pc_orders_found": "You have not requested any custom PCs yet.", "pc_order_card_title": "Custom PC Order", "button_confirm_quote": "Confirm Order & Proceed to Payment", "button_cancel_order": "Cancel Order", "button_pay_now": "Pay Now", "button_confirm_delivery": "I Have Received My Order", "js_confirm_action_title": "Confirm Action", "js_confirm_action_msg": "Are you sure you want to perform this action?", "order_status": "Status", "pc_order_cancellation_confirmation": "Are you sure you want to permanently delete this order request? This action cannot be undone.", "pc_order_delivery_confirmation_warning": "By confirming delivery, you acknowledge the item is received and cannot be returned for a refund. Are you sure?", "pc_order_no_refund_warning": "Please Note: Once payment is made, the order cannot be cancelled or refunded as assembly begins immediately.", "pc_order_in_progress_notice": "Notice: Assembly is in progress. This order can no longer be cancelled or refunded."}