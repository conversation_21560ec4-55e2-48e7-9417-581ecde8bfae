<?php
// Setup PC Components Database Tables
require_once 'PHP/config.php';

// Read the SQL file
$sql_content = file_get_contents('SQL/pc_components_tables.sql');

// Remove the USE statement since we're already connecting to the database
$sql_content = preg_replace('/USE\s+kelvinkms;/', '', $sql_content);

// Split the SQL into individual statements
$statements = explode(';', $sql_content);

// Get database connection
$link = get_db_connection();

if (!$link) {
    die("Database connection failed: " . mysqli_connect_error());
}

echo "Setting up PC Components database tables...\n";

$success_count = 0;
$error_count = 0;

foreach ($statements as $statement) {
    $statement = trim($statement);
    if (empty($statement) || strpos($statement, '--') === 0) {
        continue;
    }
    
    if (mysqli_query($link, $statement)) {
        $success_count++;
        echo "✓ Successfully executed statement\n";
    } else {
        $error_count++;
        echo "✗ Error executing statement: " . mysqli_error($link) . "\n";
        echo "Statement: " . substr($statement, 0, 100) . "...\n";
    }
}

close_db_connection($link);

echo "\n=== Setup Complete ===\n";
echo "Successful statements: $success_count\n";
echo "Failed statements: $error_count\n";

if ($error_count === 0) {
    echo "All PC Components tables have been created successfully!\n";
} else {
    echo "Some errors occurred. Please check the output above.\n";
}
?>