<?php
// Setup PC Components Database Tables - Web Version
require_once 'PHP/config.php';

// Basic security - only allow localhost access
if (!in_array($_SERVER['REMOTE_ADDR'], ['127.0.0.1', '::1'])) {
    die('Access denied. This script can only be run from localhost.');
}

echo "<!DOCTYPE html>
<html>
<head>
    <title>PC Components Database Setup</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        pre { background: #f5f5f5; padding: 10px; border-radius: 5px; }
    </style>
</head>
<body>
    <h1>PC Components Database Setup</h1>";

// Read the SQL file
$sql_file = 'SQL/pc_components_tables.sql';
if (!file_exists($sql_file)) {
    echo "<p class='error'>SQL file not found: $sql_file</p>";
    exit;
}

$sql_content = file_get_contents($sql_file);

// Remove the USE statement since we're already connecting to the database
$sql_content = preg_replace('/USE\s+kelvinkms;/', '', $sql_content);

// Split the SQL into individual statements
$statements = explode(';', $sql_content);

// Get database connection
$link = get_db_connection();

if (!$link) {
    echo "<p class='error'>Database connection failed: " . mysqli_connect_error() . "</p>";
    exit;
}

echo "<p class='info'>Setting up PC Components database tables...</p>";

$success_count = 0;
$error_count = 0;

foreach ($statements as $i => $statement) {
    $statement = trim($statement);
    if (empty($statement) || strpos($statement, '--') === 0) {
        continue;
    }
    
    echo "<p>Executing statement " . ($i + 1) . "...</p>";
    
    if (mysqli_query($link, $statement)) {
        $success_count++;
        echo "<p class='success'>✓ Successfully executed</p>";
    } else {
        $error_count++;
        echo "<p class='error'>✗ Error: " . mysqli_error($link) . "</p>";
        echo "<pre>" . htmlspecialchars(substr($statement, 0, 200)) . "...</pre>";
    }
}

close_db_connection($link);

echo "<hr>";
echo "<h2>Setup Complete</h2>";
echo "<p><strong>Successful statements:</strong> $success_count</p>";
echo "<p><strong>Failed statements:</strong> $error_count</p>";

if ($error_count === 0) {
    echo "<p class='success'><strong>All PC Components tables have been created successfully!</strong></p>";
    echo "<p>You can now test the PC Builder functionality in the member dashboard.</p>";
} else {
    echo "<p class='error'><strong>Some errors occurred. Please check the output above.</strong></p>";
}

echo "</body></html>";
?>